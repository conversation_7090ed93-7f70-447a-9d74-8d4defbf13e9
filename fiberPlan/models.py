from django.utils import timezone
from django.db import models

# Create your models here.
class Vault(models.Model):
    name = models.CharField(max_length=100)
    lat = models.DecimalField(max_digits=12, decimal_places=10)
    lng = models.DecimalField(max_digits=12, decimal_places=10)
    is_enc = models.BooleanField(default=False)
    is_mini = models.BooleanField(default=False)
    address = models.CharField(max_length=200)
    geolocation = models.CharField(max_length=100)
    notes = models.Char<PERSON>ield(max_length=255)

    def __str__(self):
        return f"{self.name}"

class FiberColors(models.Model):
    name = models.CharField(max_length=15)
    hex = models.CharField(max_length=6)

class NextVaults(models.Model):
    vault_id = models.ForeignKey(Vault, on_delete=models.CASCADE)
    next_vault_id = models.ForeignKey(Vault, on_delete=models.CASCADE, related_name='next_vaults')
    dist_to_next_vault = models.IntegerField()
    strands = models.IntegerField()

class Ticket(models.Model):
    name = models.CharField(max_length=100)
    priority = models.CharField(max_length=20, default='NORM')
    ticket = models.CharField(max_length=100)
    street = models.CharField(max_length=100)
    cross = models.CharField(max_length=100)
    place = models.CharField(max_length=100)
    done_for = models.CharField(max_length=100)
    work_type = models.CharField(max_length=100)
    work_date = models.CharField(max_length=100)
    expires = models.CharField(max_length=100)
    update_by = models.CharField(max_length=100)
    response_due = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class CostQuest(models.Model):
    location = models.CharField(max_length=100)
    address = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zip = models.CharField(max_length=100)
    county_geoid = models.CharField(max_length=10)
    block_geoid = models.CharField(max_length=50)
    lat = models.DecimalField(max_digits=12, decimal_places=10)
    lng = models.DecimalField(max_digits=12, decimal_places=10)
    fcc_rel = models.CharField(max_length=10)

class Availability(models.Model):
    id = models.IntegerField(primary_key=True)
    location = models.OneToOneField(CostQuest, on_delete=models.CASCADE)
    technology = models.CharField(max_length=100)
    max_download = models.IntegerField()
    max_upload = models.IntegerField()
    low_latency = models.IntegerField()
    business_residential_code = models.CharField(max_length=1)

class Customer(models.Model):
    clientId = models.IntegerField()
    firstName = models.CharField(max_length=100)
    lastName = models.CharField(max_length=100)
    isLead = models.BooleanField(default=False)
    isCustomer = models.BooleanField(default=False)
    street1 = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    lat = models.DecimalField(max_digits=12, decimal_places=10)
    lng = models.DecimalField(max_digits=12, decimal_places=10)
    email = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'fiberPlan_customer'

    def __str__(self):
        return f"{self.firstName} {self.lastName}"

class FiberSignupForm(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    plan_selected = models.TextField(blank=True, null=True)
    install_fee = models.FloatField(blank=True, null=True)
    network_maintenance_fee = models.FloatField(blank=True, null=True)
    modem_lease_fee = models.FloatField(blank=True, null=True)
    router_lease_fee = models.FloatField(blank=True, null=True)
    additional_construction_costs = models.FloatField(blank=True, null=True)
    preferred_installation_date = models.DateField(blank=True, null=True)
    agrees_to_terms = models.BooleanField(default=False)
    signature = models.TextField(blank=True, null=True)
    signed_date = models.DateField(blank=True, null=True)

    class Meta:
        db_table = 'form_FiberSignupForm'

    def __str__(self):
        return f"Fiber Signup Form for {self.customer}"


class SignupToken(models.Model):
    customer = models.OneToOneField(Customer, on_delete=models.CASCADE)
    token = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()

    def is_valid(self):
        from django.utils import timezone
        return (not self.used) and (self.expires_at > timezone.now())

    def __str__(self):
        return f"SignupToken for {self.customer} - {'Used' if self.used else 'Valid'}"